# 🔧 Environment Variables Documentation

This document provides detailed information about all environment variables used in the Ignition API, including the new Multi-Agent System configuration.

## 📋 Quick Setup

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Fill in your actual values in `.env`

3. **NEVER commit `.env` to version control** - it contains sensitive data

## 🏗️ Core Django Configuration

### `SECRET_KEY`
- **Required**: Yes
- **Description**: Django secret key for cryptographic signing
- **Example**: `django-insecure-a9_s1(-lx5ry97l_51^+=rj2yy7w=zhsrcv=2z4@(pt$2*7)7r`
- **Generate**: `python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"`

### Database Configuration
- **`DB_HOST`**: Database host (default: `localhost`)
- **`DB_NAME`**: Database name (default: `ignition_dev`)
- **`DB_USER`**: Database username (default: `root`)
- **`DB_PASSWORD`**: Database password
- **`DB_PORT`**: Database port (default: `3306`)

### Security & CORS
- **`ALLOWED_HOSTS`**: Comma-separated list of allowed hosts
- **`CORS_ORIGIN_WHITELIST`**: Comma-separated list of allowed CORS origins
- **`FRONTEND_BASE_URL`**: Frontend application URL
- **`URL_FRONTEND`**: Frontend URL for redirects
- **`URL_BACKEND`**: Backend API URL

## 🔐 Authentication & OAuth

### Google OAuth2
- **`GOOGLE_OAUTH2_CLIENT_ID`**: Google OAuth2 client ID
- **`GOOGLE_OAUTH2_CLIENT_SECRET`**: Google OAuth2 client secret
- **Get from**: [Google Console](https://console.developers.google.com/)

## 📧 Email Configuration (SendGrid)

- **`SENDGRID_API_KEY`**: SendGrid API key for email sending
- **`SENDGRID_FROM_NAME`**: Display name for outgoing emails
- **`FROM_EMAIL`**: From email address
- **Get from**: [SendGrid](https://app.sendgrid.com/settings/api_keys)

## 📱 SMS Configuration

### Provider Selection
- **`SMS_PROVIDER`**: Options: `aws_sns`, `vonage`, `disabled`

### AWS SNS (when SMS_PROVIDER=aws_sns)
- **`AWS_ACCESS_KEY_ID`**: AWS access key
- **`AWS_SECRET_ACCESS_KEY`**: AWS secret key
- **`AWS_DEFAULT_REGION`**: AWS region (e.g., `ap-southeast-1`)

### Vonage (when SMS_PROVIDER=vonage)
- **`VONAGE_API_KEY`**: Vonage API key
- **`VONAGE_API_SECRET`**: Vonage API secret
- **`VONAGE_FROM_NUMBER`**: Sender ID

## 🤖 AI Providers Configuration

### Primary Provider Selection
- **`AI_PROVIDER`**: Options: `openai`, `openrouter`, `google`

### OpenAI
- **`OPENAI_API_KEY`**: OpenAI API key
- **`OPENAI_DEFAULT_MODEL`**: Default model (e.g., `gpt-4o-mini`)
- **`ASSISTANT_ID`**: OpenAI Assistant ID
- **Get from**: [OpenAI Platform](https://platform.openai.com/api-keys)

### OpenRouter (Alternative to OpenAI)
- **`OPENROUTER_API_KEY`**: OpenRouter API key
- **`OPENROUTER_DEFAULT_MODEL`**: Default model
- **`OPENROUTER_APP_NAME`**: Your app name
- **`OPENROUTER_SITE_URL`**: Your website URL
- **Get from**: [OpenRouter](https://openrouter.ai/keys)

**Popular OpenRouter Models:**
- `moonshotai/kimi-dev-72b:free`
- `deepseek/deepseek-r1-0528:free`
- `qwen/qwen3-235b-a22b:free`
- `anthropic/claude-3.5-sonnet`

### Google AI
- **`GOOGLE_AI_API_KEY`**: Google AI API key
- **`GOOGLE_AI_DEFAULT_MODEL`**: Default model (e.g., `gemini-2.0-flash`)
- **`USE_GEMINI_PRO_FOR_PLAN_CREATION`**: Use Gemini Pro for plan creation
- **`GOOGLE_AI_PLAN_CREATION_MODEL`**: Model for plan creation
- **Get from**: [Google AI Studio](https://makersuite.google.com/app/apikey)

## 🚀 Multi-Agent System Configuration

### LangGraph Core Settings
- **`LANGGRAPH_DEBUG`**: Enable debug mode (`true`/`false`)
- **`LANGGRAPH_TRACING_V2`**: Enable tracing (`true`/`false`)
- **`LANGGRAPH_API_URL`**: LangGraph API URL
- **`LANGGRAPH_API_KEY`**: Optional LangGraph API key

### Multi-Agent Workflow
- **`MULTI_AGENT_ENABLED`**: Enable multi-agent system (`true`/`false`)
- **`MULTI_AGENT_MAX_RETRIES`**: Max retries per agent (default: `3`)
- **`MULTI_AGENT_TIMEOUT_SECONDS`**: Global timeout (default: `300`)
- **`MULTI_AGENT_PARALLEL_EXECUTION`**: Enable parallel execution (`true`/`false`)

### Agent-Specific Configuration

#### Domain Classification Agent
- **`DOMAIN_AGENT_TIMEOUT`**: Timeout in seconds (default: `60`)
- **`DOMAIN_AGENT_MAX_RETRIES`**: Max retries (default: `2`)
- **`DOMAIN_AGENT_CONFIDENCE_THRESHOLD`**: Minimum confidence (default: `0.8`)

#### Structure Optimization Agent
- **`STRUCTURE_AGENT_TIMEOUT`**: Timeout in seconds (default: `90`)
- **`STRUCTURE_AGENT_MAX_RETRIES`**: Max retries (default: `2`)
- **`STRUCTURE_AGENT_MAX_MILESTONES`**: Max milestones (default: `7`)
- **`STRUCTURE_AGENT_MAX_TASKS_PER_MILESTONE`**: Max tasks per milestone (default: `7`)

#### Content Generation Agent
- **`CONTENT_AGENT_TIMEOUT`**: Timeout in seconds (default: `120`)
- **`CONTENT_AGENT_MAX_RETRIES`**: Max retries (default: `2`)
- **`CONTENT_AGENT_MIN_NAME_WORDS`**: Min words in names (default: `7`)
- **`CONTENT_AGENT_MAX_NAME_WORDS`**: Max words in names (default: `15`)
- **`CONTENT_AGENT_MIN_DESCRIPTION_WORDS`**: Min words in descriptions (default: `30`)
- **`CONTENT_AGENT_MAX_DESCRIPTION_WORDS`**: Max words in descriptions (default: `60`)

#### Timeline Optimization Agent
- **`TIMELINE_AGENT_TIMEOUT`**: Timeout in seconds (default: `90`)
- **`TIMELINE_AGENT_MAX_RETRIES`**: Max retries (default: `2`)
- **`TIMELINE_AGENT_BUFFER_PERCENTAGE`**: Buffer time percentage (default: `0.15`)

#### Validation Agent
- **`VALIDATION_AGENT_TIMEOUT`**: Timeout in seconds (default: `60`)
- **`VALIDATION_AGENT_MAX_RETRIES`**: Max retries (default: `1`)
- **`VALIDATION_AGENT_MIN_QUALITY_SCORE`**: Min quality score (default: `0.8`)

#### Quality Enhancement Agent
- **`QUALITY_AGENT_TIMEOUT`**: Timeout in seconds (default: `120`)
- **`QUALITY_AGENT_MAX_RETRIES`**: Max retries (default: `2`)
- **`QUALITY_AGENT_TARGET_QUALITY_SCORE`**: Target quality score (default: `0.9`)

### Workflow Performance Settings
- **`WORKFLOW_MAX_CONCURRENT_AGENTS`**: Max concurrent agents (default: `2`)
- **`WORKFLOW_STATE_PERSISTENCE`**: Enable state persistence (`true`/`false`)
- **`WORKFLOW_PROGRESS_TRACKING`**: Enable progress tracking (`true`/`false`)
- **`WORKFLOW_ERROR_RECOVERY`**: Enable error recovery (`true`/`false`)
- **`WORKFLOW_CHECKPOINT_INTERVAL`**: Checkpoint interval in seconds (default: `30`)

### Quality Gates Configuration
- **`QUALITY_GATES_ENABLED`**: Enable quality gates (`true`/`false`)
- **`QUALITY_GATES_MIN_SCORE`**: Minimum quality score (default: `0.8`)
- **`QUALITY_GATES_AUTO_FIX`**: Enable auto-fix suggestions (`true`/`false`)

### Progress Tracking Configuration
- **`PROGRESS_TRACKING_ENABLED`**: Enable progress tracking (`true`/`false`)
- **`PROGRESS_TRACKING_CALLBACKS`**: Enable callbacks (`true`/`false`)
- **`PROGRESS_TRACKING_METRICS`**: Enable metrics collection (`true`/`false`)

## 🔧 Development & Debugging

- **`DEBUG`**: Django debug mode (`true`/`false`) - **NEVER true in production**
- **`LOG_LEVEL`**: Logging level (`DEBUG`, `INFO`, `WARNING`, `ERROR`)
- **`MULTI_AGENT_LOG_LEVEL`**: Multi-agent system log level
- **`ENABLE_PERFORMANCE_MONITORING`**: Enable performance monitoring
- **`ENABLE_AGENT_METRICS`**: Enable agent metrics collection

## 🚀 Production Settings

### Security (Uncomment for production)
- **`SECURE_HSTS_SECONDS`**: HSTS header duration (e.g., `31536000`)
- **`SECURE_SSL_REDIRECT`**: Force HTTPS redirect (`true`/`false`)
- **`SESSION_COOKIE_SECURE`**: Secure session cookies (`true`/`false`)
- **`CSRF_COOKIE_SECURE`**: Secure CSRF cookies (`true`/`false`)

### Database & Caching
- **`DB_CONN_MAX_AGE`**: Database connection max age (e.g., `600`)
- **`DB_CONN_HEALTH_CHECKS`**: Enable connection health checks
- **`REDIS_URL`**: Redis connection URL
- **`CACHE_TTL`**: Cache time-to-live in seconds

### Background Tasks (Celery)
- **`CELERY_BROKER_URL`**: Celery broker URL
- **`CELERY_RESULT_BACKEND`**: Celery result backend URL

## 🔗 Optional Integrations

- **`SENTRY_DSN`**: Sentry error tracking DSN
- **`GOOGLE_ANALYTICS_ID`**: Google Analytics tracking ID
- **`AWS_STORAGE_BUCKET_NAME`**: S3 bucket for file storage
- **`AWS_S3_REGION_NAME`**: S3 region
- **`USE_S3_STORAGE`**: Enable S3 storage (`true`/`false`)

## 🛡️ Security Best Practices

1. **Never commit `.env` to version control**
2. **Use strong, unique SECRET_KEY in production**
3. **Enable HTTPS in production** (set SSL redirect variables)
4. **Use environment-specific databases**
5. **Rotate API keys regularly**
6. **Set DEBUG=false in production**
7. **Use secure cookie settings in production**

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Check if all required packages are installed
2. **Database Connection**: Verify database credentials and server status
3. **API Key Issues**: Ensure API keys are valid and have proper permissions
4. **CORS Errors**: Check CORS_ORIGIN_WHITELIST includes your frontend URL
5. **Multi-Agent Timeouts**: Adjust timeout values based on your needs

### Testing Configuration

```bash
# Test database connection
python manage.py check --database default

# Test multi-agent system
python -c "from multi_agent import IgnitionPlanOrchestrator; print('✅ Multi-agent system ready')"

# Test AI providers
python -c "from assistant.ai_providers import get_ai_client; print('✅ AI provider ready')"
```
