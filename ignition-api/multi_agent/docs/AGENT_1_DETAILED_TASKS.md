# 🤖 **AGENT 1: DOMAIN CLASSIFICATION AGENT - CHI TIẾT TASKS**

## 🎯 **TỔNG QUAN AGENT 1**

**Vai trò**: Chuyên gia phân tích domain và extract requirements từ user input
**Vị trí trong pipeline**: Agent <PERSON><PERSON><PERSON> tiê<PERSON>, nhận raw user input
**<PERSON><PERSON><PERSON> tiêu chính**: Transform natural language thành structured domain analysis

---

## 📋 **BREAKDOWN CHI TIẾT TASKS**

### **Task 2.1: Implement Domain Classification Agent**
**Mục tiêu**: Tạo core agent class với domain analysis capabilities

#### **Subtask 2.1.1: Create Agent Class Structure**
**📥 INPUT:** BaseIgnitionAgent abstract class, domain analysis requirements

**🔧 THỰC HIỆN:**
```python
# agents/domain_classifier.py
from multi_agent.base_agent import BaseIgnitionAgent
from multi_agent.utils.state_management import PlanGenerationState
from assistant.ai_providers import create_chat_completion
import json
import logging

class DomainClassificationAgent(BaseIgnitionAgent):
    def __init__(self):
        super().__init__(
            agent_name="domain_classifier",
            config={
                "primary_model": "gemini-2.0-flash-exp",
                "fallback_model": "gemini-1.5-pro",
                "temperature": 0.1,
                "max_tokens": 1500,
                "timeout": 30
            }
        )
        self.domain_patterns = self._load_domain_patterns()
        self.confidence_threshold = 0.7
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Main domain analysis logic"""
        # Implementation sẽ được detail trong subtasks khác
        pass
```

**📤 OUTPUT:** DomainClassificationAgent class skeleton với proper inheritance
**⏱️ Duration:** 4 giờ

---

#### **Subtask 2.1.2: Implement Domain Pattern Recognition**
**📥 INPUT:** User input text, domain classification database

**🔧 THỰC HIỆN:**
```python
def _load_domain_patterns(self) -> dict:
    """Load domain classification patterns"""
    return {
        "mobile_app_development": {
            "keywords": ["app", "mobile", "ios", "android", "ứng dụng"],
            "indicators": ["tải về", "app store", "play store"],
            "complexity_factors": ["payment", "real-time", "offline"]
        },
        "web_development": {
            "keywords": ["website", "web", "trang web", "online"],
            "indicators": ["domain", "hosting", "responsive"],
            "complexity_factors": ["e-commerce", "cms", "api"]
        },
        "data_science": {
            "keywords": ["phân tích", "dữ liệu", "machine learning", "AI"],
            "indicators": ["dataset", "model", "prediction"],
            "complexity_factors": ["big data", "real-time", "deep learning"]
        },
        "e_commerce": {
            "keywords": ["bán hàng", "shop", "cửa hàng", "thương mại"],
            "indicators": ["thanh toán", "giỏ hàng", "sản phẩm"],
            "complexity_factors": ["inventory", "multi-vendor", "international"]
        }
    }

def _classify_primary_domain(self, user_input: str) -> tuple:
    """Classify primary domain based on keywords and patterns"""
    scores = {}
    
    for domain, patterns in self.domain_patterns.items():
        score = 0
        
        # Keyword matching
        for keyword in patterns["keywords"]:
            if keyword.lower() in user_input.lower():
                score += 2
        
        # Indicator matching  
        for indicator in patterns["indicators"]:
            if indicator.lower() in user_input.lower():
                score += 3
                
        # Complexity factor detection
        complexity_score = 0
        for factor in patterns["complexity_factors"]:
            if factor.lower() in user_input.lower():
                complexity_score += 1
                
        scores[domain] = {
            "base_score": score,
            "complexity_score": complexity_score,
            "total_score": score + complexity_score * 0.5
        }
    
    # Find best match
    best_domain = max(scores.keys(), key=lambda x: scores[x]["total_score"])
    confidence = min(scores[best_domain]["total_score"] / 10, 1.0)
    
    return best_domain, confidence, scores
```

**📤 OUTPUT:** Domain classification logic với confidence scoring
**⏱️ Duration:** 6 giờ

---

#### **Subtask 2.1.3: Implement Requirement Extraction**
**📥 INPUT:** User input, classified domain, domain patterns

**🔧 THỰC HIỆN:**
```python
def _extract_requirements(self, user_input: str, primary_domain: str) -> dict:
    """Extract functional, non-functional, and technical requirements"""
    
    # Requirement extraction patterns
    requirement_patterns = {
        "functional": {
            "user_management": ["đăng nhập", "tài khoản", "user", "authentication"],
            "payment_processing": ["thanh toán", "payment", "momo", "visa", "mastercard"],
            "product_catalog": ["sản phẩm", "danh mục", "catalog", "inventory"],
            "search_functionality": ["tìm kiếm", "search", "filter", "sort"],
            "shopping_cart": ["giỏ hàng", "cart", "add to cart", "checkout"],
            "order_management": ["đơn hàng", "order", "tracking", "history"],
            "reviews_ratings": ["đánh giá", "review", "rating", "comment"],
            "notifications": ["thông báo", "notification", "alert", "push"]
        },
        "non_functional": {
            "performance": ["nhanh", "fast", "performance", "tốc độ"],
            "security": ["bảo mật", "secure", "an toàn", "encryption"],
            "scalability": ["mở rộng", "scale", "nhiều user", "traffic"],
            "usability": ["dễ dùng", "user-friendly", "intuitive", "UX"],
            "reliability": ["ổn định", "reliable", "uptime", "available"],
            "mobile_responsive": ["mobile", "responsive", "điện thoại", "tablet"]
        },
        "technical": {
            "mobile_app": ["app", "mobile", "ios", "android"],
            "web_application": ["website", "web app", "browser"],
            "backend_api": ["api", "backend", "server", "database"],
            "database_design": ["database", "data storage", "mysql", "mongodb"],
            "cloud_hosting": ["cloud", "aws", "azure", "hosting"],
            "third_party_integration": ["integration", "api", "payment gateway"]
        }
    }
    
    extracted = {
        "functional": [],
        "non_functional": [],
        "technical": []
    }
    
    for req_type, categories in requirement_patterns.items():
        for req_name, keywords in categories.items():
            for keyword in keywords:
                if keyword.lower() in user_input.lower():
                    if req_name not in extracted[req_type]:
                        extracted[req_type].append(req_name)
                    break
    
    # Domain-specific requirement enhancement
    if primary_domain == "mobile_app_development":
        if "e_commerce" in user_input.lower() or "bán hàng" in user_input.lower():
            extracted["functional"].extend([
                "product_catalog", "shopping_cart", "payment_processing"
            ])
            extracted["technical"].extend([
                "mobile_app", "backend_api", "database_design"
            ])
    
    return extracted
```

**📤 OUTPUT:** Structured requirements extraction với domain-specific logic
**⏱️ Duration:** 8 giờ

---

#### **Subtask 2.1.4: Implement Constraint Analysis**
**📥 INPUT:** User input, duration preference, implicit constraints

**🔧 THỰC HIỆN:**
```python
def _analyze_constraints(self, user_input: str, duration: str) -> dict:
    """Analyze project constraints from input"""

    constraints = {
        "time": self._parse_duration(duration),
        "budget": "medium",  # Default
        "team_size": "small_team_3_5_people",  # Default
        "technical_expertise": "intermediate"  # Default
    }

    # Budget indicators
    budget_indicators = {
        "low": ["ít tiền", "budget thấp", "tiết kiệm", "rẻ", "free"],
        "high": ["không giới hạn", "cao cấp", "premium", "enterprise"]
    }
    
    for budget_level, indicators in budget_indicators.items():
        for indicator in indicators:
            if indicator in user_input.lower():
                constraints["budget"] = budget_level
                break
    
    # Team size indicators
    team_indicators = {
        "solo": ["một mình", "tự làm", "solo", "individual"],
        "large_team": ["team lớn", "nhiều người", "công ty", "enterprise"]
    }
    
    for team_size, indicators in team_indicators.items():
        for indicator in indicators:
            if indicator in user_input.lower():
                constraints["team_size"] = team_size
                break
    
    # Technical expertise indicators
    expertise_indicators = {
        "beginner": ["mới bắt đầu", "beginner", "học", "không biết"],
        "advanced": ["chuyên gia", "expert", "advanced", "professional"]
    }
    
    for level, indicators in expertise_indicators.items():
        for indicator in indicators:
            if indicator in user_input.lower():
                constraints["technical_expertise"] = level
                break
    
    return constraints

def _parse_duration(self, duration_str: str) -> str:
    """Parse duration string into standardized format"""
    duration_lower = duration_str.lower()
    
    if "tuần" in duration_lower or "week" in duration_lower:
        return f"{duration_str}_weeks"
    elif "tháng" in duration_lower or "month" in duration_lower:
        return f"{duration_str}_months"
    elif "năm" in duration_lower or "year" in duration_lower:
        return f"{duration_str}_years"
    else:
        return duration_str
```

**📤 OUTPUT:** Constraint analysis logic với intelligent parsing
**⏱️ Duration:** 4 giờ

---

#### **Subtask 2.1.5: Implement Success Metrics & Stakeholder Identification**
**📥 INPUT:** Domain analysis, requirements, project context

**🔧 THỰC HIỆN:**
```python
def _identify_success_metrics(self, domain: str, requirements: dict) -> list:
    """Identify relevant success metrics based on domain and requirements"""
    
    domain_metrics = {
        "mobile_app_development": [
            "app_store_rating",
            "download_count", 
            "user_retention_rate",
            "daily_active_users",
            "crash_rate"
        ],
        "e_commerce": [
            "conversion_rate",
            "average_order_value",
            "customer_acquisition_cost",
            "customer_lifetime_value",
            "cart_abandonment_rate"
        ],
        "web_development": [
            "page_load_speed",
            "bounce_rate",
            "user_engagement_time",
            "seo_ranking",
            "conversion_rate"
        ]
    }
    
    base_metrics = domain_metrics.get(domain, [
        "user_adoption_rate",
        "user_satisfaction_score",
        "system_uptime",
        "performance_metrics"
    ])
    
    # Add requirement-specific metrics
    if "payment_processing" in requirements.get("functional", []):
        base_metrics.append("payment_success_rate")
    
    if "user_management" in requirements.get("functional", []):
        base_metrics.append("user_registration_rate")
    
    return base_metrics[:5]  # Limit to top 5 metrics

def _identify_stakeholders(self, domain: str, requirements: dict) -> list:
    """Identify project stakeholders"""
    
    base_stakeholders = ["end_users", "development_team", "project_owner"]
    
    # Domain-specific stakeholders
    if domain == "e_commerce":
        base_stakeholders.extend(["customers", "vendors", "payment_providers"])
    elif domain == "mobile_app_development":
        base_stakeholders.extend(["app_store_reviewers", "device_manufacturers"])
    
    # Requirement-specific stakeholders
    if "payment_processing" in requirements.get("functional", []):
        base_stakeholders.append("financial_institutions")
    
    if "user_management" in requirements.get("functional", []):
        base_stakeholders.append("data_protection_officers")
    
    return list(set(base_stakeholders))  # Remove duplicates
```

**📤 OUTPUT:** Success metrics và stakeholder identification logic
**⏱️ Duration:** 3 giờ

---

#### **Subtask 2.1.6: Implement AI-Enhanced Analysis**
**📥 INPUT:** Structured analysis từ previous subtasks, AI prompts

**🔧 THỰC HIỆN:**
```python
async def _enhance_with_ai(self, preliminary_analysis: dict, user_input: str) -> dict:
    """Enhance analysis using AI for deeper insights"""
    
    system_prompt = """
    Bạn là chuyên gia phân tích dự án với 15+ năm kinh nghiệm trong việc phân tích requirements và domain classification.
    
    Nhiệm vụ: Phân tích và cải thiện domain analysis đã có, bổ sung insights sâu hơn.
    
    Hãy:
    1. Xác nhận hoặc điều chỉnh primary_domain
    2. Bổ sung sub_domains quan trọng
    3. Đánh giá lại complexity_level
    4. Thêm requirements có thể bị bỏ sót
    5. Xác định risk factors tiềm ẩn
    6. Đề xuất technology stack phù hợp
    
    Trả về JSON format với cấu trúc tương tự input nhưng được enhanced.
    """
    
    user_prompt = f"""
    Phân tích sơ bộ:
    {json.dumps(preliminary_analysis, indent=2, ensure_ascii=False)}
    
    User input gốc: "{user_input}"
    
    Hãy phân tích sâu hơn và cải thiện analysis này.
    """
    
    try:
        ai_response = await create_chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            model=self.config["primary_model"],
            temperature=self.config["temperature"],
            max_tokens=self.config["max_tokens"]
        )
        
        enhanced_analysis = json.loads(ai_response["content"])
        
        # Merge với preliminary analysis
        final_analysis = preliminary_analysis.copy()
        final_analysis.update(enhanced_analysis)
        
        # Add AI confidence score
        final_analysis["ai_enhancement_confidence"] = 0.9
        
        return final_analysis
        
    except Exception as e:
        self.logger.warning(f"AI enhancement failed: {e}")
        # Return preliminary analysis if AI fails
        preliminary_analysis["ai_enhancement_confidence"] = 0.0
        return preliminary_analysis
```

**📤 OUTPUT:** AI-enhanced domain analysis với deeper insights
**⏱️ Duration:** 6 giờ

---

#### **Subtask 2.1.7: Implement Main Process Method**
**📥 INPUT:** All previous subtask implementations, PlanGenerationState

**🔧 THỰC HIỆN:**
```python
async def process(self, state: PlanGenerationState) -> dict:
    """Main domain analysis process"""
    
    user_input = state["user_input"]
    duration = state["duration"]
    
    try:
        # Step 1: Pattern-based classification
        primary_domain, confidence, domain_scores = self._classify_primary_domain(user_input)
        
        # Step 2: Extract requirements
        requirements = self._extract_requirements(user_input, primary_domain)
        
        # Step 3: Analyze constraints
        constraints = self._analyze_constraints(user_input, duration)
        
        # Step 4: Identify success metrics and stakeholders
        success_metrics = self._identify_success_metrics(primary_domain, requirements)
        stakeholders = self._identify_stakeholders(primary_domain, requirements)
        
        # Step 5: Create preliminary analysis
        preliminary_analysis = {
            "primary_domain": primary_domain,
            "sub_domains": self._extract_sub_domains(user_input, primary_domain),
            "complexity_level": self._assess_complexity(requirements, constraints),
            "confidence_score": confidence,
            "extracted_requirements": requirements,
            "constraints": constraints,
            "success_metrics": success_metrics,
            "stakeholders": stakeholders,
            "domain_scores": domain_scores
        }
        
        # Step 6: AI enhancement
        if confidence < self.confidence_threshold:
            enhanced_analysis = await self._enhance_with_ai(preliminary_analysis, user_input)
        else:
            enhanced_analysis = preliminary_analysis
            enhanced_analysis["ai_enhancement_confidence"] = 1.0
        
        # Step 7: Final validation
        if not self.validate_output({"domain_analysis": enhanced_analysis}):
            raise ValueError("Domain analysis validation failed")
        
        return {
            "domain_analysis": enhanced_analysis,
            "progress": 16.67  # 1/6 agents completed
        }
        
    except Exception as e:
        self.logger.error(f"Domain analysis failed: {e}")
        raise e

def validate_output(self, output: dict) -> bool:
    """Validate domain analysis output"""
    analysis = output.get("domain_analysis", {})
    
    required_fields = [
        "primary_domain", "complexity_level", "extracted_requirements",
        "constraints", "success_metrics", "stakeholders"
    ]
    
    return all(field in analysis for field in required_fields)
```

**📤 OUTPUT:** Complete domain analysis process với error handling
**⏱️ Duration:** 4 giờ

---

### **Task 2.2: Create Domain Analysis Prompts**
**Mục tiêu**: Thiết kế optimal prompts cho AI enhancement

#### **Subtask 2.2.1: Design System Prompts**
**📥 INPUT:** Domain analysis requirements, AI best practices

**🔧 THỰC HIỆN:**
- Create role-based system prompts
- Define output format specifications
- Add domain expertise context

**📤 OUTPUT:**
```python
# prompts/domain_analysis_prompts.py
DOMAIN_ANALYSIS_SYSTEM_PROMPT = """
Bạn là Senior Business Analyst và Domain Expert với 15+ năm kinh nghiệm trong việc phân tích và classification các dự án công nghệ.

CHUYÊN MÔN:
- Domain classification (Mobile, Web, Data Science, E-commerce, etc.)
- Requirements engineering và analysis
- Technology stack recommendation
- Project complexity assessment
- Risk analysis và mitigation strategies

NHIỆM VỤ:
Phân tích user input và tạo ra comprehensive domain analysis bao gồm:

1. PRIMARY DOMAIN CLASSIFICATION
   - Xác định domain chính (mobile_app, web_app, data_science, e_commerce, etc.)
   - Confidence score (0.0-1.0)
   - Reasoning cho classification

2. SUB-DOMAINS IDENTIFICATION  
   - Các sub-domains liên quan
   - Mức độ quan trọng của từng sub-domain

3. COMPLEXITY ASSESSMENT
   - Level: beginner/intermediate/advanced/expert
   - Complexity factors và reasoning

4. REQUIREMENTS EXTRACTION
   - Functional requirements (features, capabilities)
   - Non-functional requirements (performance, security, etc.)
   - Technical requirements (technology, infrastructure)

5. CONSTRAINTS ANALYSIS
   - Time constraints
   - Budget limitations
   - Team size và skill requirements
   - Technical constraints

6. SUCCESS METRICS
   - KPIs phù hợp với domain
   - Measurable outcomes
   - Business objectives

7. STAKEHOLDER IDENTIFICATION
   - Primary stakeholders
   - Secondary stakeholders
   - Their interests và concerns

8. RISK FACTORS
   - Technical risks
   - Business risks
   - Mitigation strategies

OUTPUT FORMAT: JSON với structure được định nghĩa rõ ràng, đầy đủ thông tin, actionable insights.
"""
```

**⏱️ Duration:** 3 giờ

---

#### **Subtask 2.2.2: Create Few-Shot Examples Database**
**📥 INPUT:** Various project types, expected outputs

**🔧 THỰC HIỆN:**
- Collect diverse project examples
- Create high-quality analysis examples
- Organize by domain categories

**📤 OUTPUT:**
```python
FEW_SHOT_EXAMPLES = {
    "mobile_app_ecommerce": {
        "input": "Tôi muốn tạo ứng dụng mobile bán quần áo thời trang cho giới trẻ",
        "output": {
            "primary_domain": "mobile_app_development",
            "sub_domains": ["e_commerce", "fashion_retail", "social_commerce"],
            "complexity_level": "intermediate",
            "confidence_score": 0.95,
            # ... complete example
        }
    },
    "web_platform": {
        "input": "Xây dựng platform học online với video streaming",
        "output": {
            "primary_domain": "web_development", 
            "sub_domains": ["education_technology", "video_streaming", "user_management"],
            # ... complete example
        }
    }
    # ... more examples
}
```

**⏱️ Duration:** 4 giờ

---

## 📊 **TỔNG KẾT AGENT 1**

### **Tổng thời gian ước tính:** 42 giờ (5-6 ngày làm việc)

### **Deliverables chính:**
1. **DomainClassificationAgent class** - Complete implementation
2. **Domain pattern recognition** - Keyword-based classification
3. **Requirement extraction** - Structured requirement analysis  
4. **Constraint analysis** - Project limitation assessment
5. **AI enhancement** - Deep analysis using LLM
6. **Prompt engineering** - Optimized prompts cho AI calls
7. **Validation system** - Output quality assurance

### **Input → Output Transformation:**
```
"Tôi muốn tạo app e-commerce bán quần áo" 
    ↓
{
    "primary_domain": "mobile_app_development",
    "sub_domains": ["e_commerce", "fashion_retail"],
    "complexity_level": "intermediate", 
    "extracted_requirements": {
        "functional": ["user_auth", "product_catalog", "payment"],
        "non_functional": ["mobile_responsive", "secure", "scalable"],
        "technical": ["mobile_app", "backend_api", "database"]
    },
    "constraints": {"time": "3_months", "team_size": "small"},
    "success_metrics": ["user_adoption", "conversion_rate"],
    "stakeholders": ["end_users", "business_owners", "dev_team"]
}
```

**Agent 1 transforms vague user ideas into structured, actionable domain analysis!** 🎯
