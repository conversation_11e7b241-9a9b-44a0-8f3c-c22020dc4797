import os
import requests
from django.core.mail import send_mail
import logging
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

logger = logging.getLogger(__name__)


def get_formatted_from_email():
    """
    Get the formatted from email with sender name for SendGrid.
    Returns email in format: "Sender Name <<EMAIL>>"
    """
    from_email = os.getenv('FROM_EMAIL')
    from_name = os.getenv('SENDGRID_FROM_NAME')
    
    if from_name and from_email:
        return f'"{from_name}" <{from_email}>'
    else:
        return from_email


def send_email_with_name(subject, message, recipient_list, fail_silently=False):
    """
    Send email using SendGrid with proper sender name formatting.

    Args:
        subject (str): Email subject
        message (str): Email message body
        recipient_list (list): List of recipient email addresses
        fail_silently (bool): Whether to fail silently on errors

    Returns:
        int: Number of emails sent successfully
    """
    from_email = get_formatted_from_email()

    # Debug logging
    logger.info(f"📧 Attempting to send email:")
    logger.info(f"  - From: {from_email}")
    logger.info(f"  - To: {recipient_list}")
    logger.info(f"  - Subject: {subject}")
    logger.info(f"  - Fail silently: {fail_silently}")

    try:
        # Try SendGrid Web API first if SMTP fails
        try:
            result = send_mail(
                subject=subject,
                message=message,
                from_email=from_email,
                recipient_list=recipient_list,
                fail_silently=fail_silently
            )
            logger.info(f"✅ Email sent via SMTP successfully. Result: {result}")
            return result
        except Exception as smtp_error:
            logger.warning(f"⚠️ SMTP failed: {str(smtp_error)}, trying SendGrid Web API...")

            # Fallback to SendGrid Web API
            result = send_email_via_sendgrid_api(subject, message, from_email, recipient_list)
            if result:
                logger.info(f"✅ Email sent via SendGrid Web API successfully")
                return 1
            else:
                raise Exception("SendGrid Web API also failed")

    except Exception as e:
        logger.error(f"❌ All email sending methods failed: {str(e)}")
        if not fail_silently:
            raise
        return 0


def send_email_via_sendgrid_api(subject, message, from_email, recipient_list):
    """
    Send email using SendGrid Web API as fallback.
    """
    try:
        import requests
        import json

        api_key = os.getenv('SENDGRID_API_KEY')
        if not api_key:
            logger.error("SendGrid API key not found")
            return False

        # Parse from_email to get name and email
        if '<' in from_email and '>' in from_email:
            # <AUTHOR> <EMAIL>
            name_part = from_email.split('<')[0].strip().strip('"')
            email_part = from_email.split('<')[1].strip('>')
        else:
            name_part = os.getenv('SENDGRID_FROM_NAME', 'Ignition App')
            email_part = from_email

        # Prepare SendGrid API payload with anti-spam headers
        data = {
            "personalizations": [
                {
                    "to": [{"email": email} for email in recipient_list]
                }
            ],
            "from": {
                "email": email_part,
                "name": name_part
            },
            "reply_to": {
                "email": "<EMAIL>",
                "name": "Ignition Support"
            },
            "subject": subject,
            "content": [
                {
                    "type": "text/plain",
                    "value": message
                },
                {
                    "type": "text/html",
                    "value": message.replace('\n', '<br>')
                }
            ],
            "categories": ["account-activation", "transactional"],
            "custom_args": {
                "type": "account_verification",
                "version": "1.0"
            }
        }

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        response = requests.post(
            "https://api.sendgrid.com/v3/mail/send",
            headers=headers,
            data=json.dumps(data),
            timeout=30
        )

        if response.status_code == 202:
            logger.info(f"✅ SendGrid Web API: Email sent successfully")
            return True
        else:
            logger.error(f"❌ SendGrid Web API error: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        logger.error(f"❌ SendGrid Web API exception: {str(e)}")
        return False


def get_sns_client():
    """
    Get AWS SNS client instance.

    Returns:
        boto3.client: SNS client instance or None if credentials are missing
    """
    try:
        # Try to create SNS client - will use IAM role if on EC2, or env vars
        sns_client = boto3.client('sns', region_name=os.getenv('AWS_DEFAULT_REGION', 'us-east-1'))

        # Test the client by getting account attributes
        sns_client.get_sms_attributes()

        return sns_client
    except NoCredentialsError:
        logger.error("AWS credentials not found. Please configure IAM role or environment variables")
        return None
    except ClientError as e:
        logger.error(f"AWS SNS client error: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error creating SNS client: {str(e)}")
        return None


def format_phone_number(phone_number):
    """
    Format phone number for AWS SNS (ensure it's in E.164 format).
    Supports US and Vietnam phone numbers.

    Args:
        phone_number (str): Phone number to format

    Returns:
        str: Formatted phone number or None if invalid
    """
    if not phone_number:
        print(f"🔍 Phone format: Empty phone number")
        return None

    print(f"🔍 Phone format: Input '{phone_number}'")

    # Remove all non-digit characters except +
    cleaned = ''.join(char for char in phone_number if char.isdigit() or char == '+')
    print(f"🔍 Phone format: Cleaned '{cleaned}'")

    # If it already starts with +, return as is
    if cleaned.startswith('+'):
        print(f"🔍 Phone format: Already E.164 format '{cleaned}'")
        return cleaned

    # Handle different country formats
    if not cleaned.startswith('+'):
        # Vietnam numbers
        if len(cleaned) == 10 and cleaned.startswith('0'):
            # Local Vietnam format: 0963361082 -> +84963361082
            cleaned = '+84' + cleaned[1:]
            print(f"🔍 Phone format: Vietnam local format -> '{cleaned}'")
        elif len(cleaned) == 9 and cleaned.startswith('9'):
            # Vietnam without leading 0: 963361082 -> +84963361082
            cleaned = '+84' + cleaned
            print(f"🔍 Phone format: Vietnam without 0 -> '{cleaned}'")
        elif len(cleaned) == 11 and cleaned.startswith('84'):
            # Vietnam with country code: 84963361082 -> +84963361082
            cleaned = '+' + cleaned
            print(f"🔍 Phone format: Vietnam with country code -> '{cleaned}'")
        # US numbers
        elif len(cleaned) == 10:
            # US number without country code: 1234567890 -> +11234567890
            cleaned = '+1' + cleaned
            print(f"🔍 Phone format: US format -> '{cleaned}'")
        elif len(cleaned) == 11 and cleaned.startswith('1'):
            # US number with 1 prefix: 11234567890 -> +11234567890
            cleaned = '+' + cleaned
            print(f"🔍 Phone format: US with 1 prefix -> '{cleaned}'")
        else:
            # For other countries or unrecognized formats
            print(f"❌ Phone format: Unrecognized format '{phone_number}' (cleaned: '{cleaned}')")
            logger.warning(f"Phone number format not recognized: {phone_number}")
            return None

    print(f"✅ Phone format: Final result '{cleaned}'")
    return cleaned


def send_sms_vonage(phone_number, message):
    """
    Send SMS using Vonage API.

    Args:
        phone_number (str): Recipient phone number (E.164 format)
        message (str): SMS message content

    Returns:
        dict: Result dictionary with success status
    """
    try:
        api_key = os.getenv('VONAGE_API_KEY')
        api_secret = os.getenv('VONAGE_API_SECRET')
        from_name = os.getenv('VONAGE_FROM_NUMBER', 'Ignition')

        if not all([api_key, api_secret]):
            print("❌ Vonage: Credentials not configured")
            missing = []
            if not api_key: missing.append('VONAGE_API_KEY')
            if not api_secret: missing.append('VONAGE_API_SECRET')
            return {"success": False, "error": f"Vonage credentials missing: {', '.join(missing)}"}

        print(f"📱 Vonage: Sending SMS to {phone_number}")
        print(f"📤 Vonage: From: {from_name}")

        # Import Vonage client (new v4+ SDK)
        from vonage import Vonage, Auth
        from vonage_sms import SmsMessage

        # Initialize Vonage client
        auth = Auth(api_key=api_key, api_secret=api_secret)
        client = Vonage(auth=auth)

        # Create SMS message
        sms_message = SmsMessage(
            from_=from_name,
            to=phone_number,
            text=message
        )

        # Send SMS using Vonage
        response = client.sms.send(sms_message)

        # Parse response
        if response.messages:
            message_data = response.messages[0]
            status = message_data.status
            message_id = message_data.message_id

            if status == "0":  # Success
                print(f"✅ Vonage: SMS sent successfully - Message ID: {message_id}")
                print(f"📊 Vonage: Status: {status}")

                # Get actual cost if available
                cost = getattr(message_data, 'message_price', None)
                if cost:
                    print(f"💰 Vonage: Cost: €{cost}")
                else:
                    cost = 0.143  # Default cost from your test
                    print(f"💰 Vonage: Estimated cost: ~€{cost:.3f}")

                return {
                    "success": True,
                    "message_id": message_id,
                    "to": phone_number,
                    "from": from_name,
                    "provider": "vonage",
                    "status": status,
                    "cost": float(cost) if cost else None,
                    "currency": "EUR"
                }
            else:
                error_text = getattr(message_data, 'error_text', f"Status code: {status}")
                print(f"❌ Vonage: SMS failed - {error_text}")

                return {
                    "success": False,
                    "error": f"Vonage error: {error_text}",
                    "provider": "vonage",
                    "status": status
                }
        else:
            print(f"❌ Vonage: No message data in response")
            return {
                "success": False,
                "error": "Vonage: No message data in response",
                "provider": "vonage"
            }

    except ImportError as e:
        error_msg = f"Vonage library not installed or import error: {str(e)}. Run: pip install vonage"
        print(f"❌ Vonage: {error_msg}")
        return {"success": False, "error": error_msg, "provider": "vonage"}

    except Exception as e:
        # Handle Vonage-specific errors
        error_msg = f"Vonage error: {str(e)}"
        print(f"❌ Vonage: {error_msg}")

        # Check for common Vonage errors
        if "authentication failed" in str(e).lower():
            error_msg = "Invalid API credentials"
        elif "insufficient credit" in str(e).lower():
            error_msg = "Insufficient account balance"
        elif "invalid number" in str(e).lower():
            error_msg = "Invalid phone number format"

        return {
            "success": False,
            "error": error_msg,
            "provider": "vonage"
        }


def send_sms_aws_sns(phone_number, message):
    """
    Send SMS using AWS SNS.

    Args:
        phone_number (str): Recipient phone number (E.164 format)
        message (str): SMS message content

    Returns:
        dict: Result dictionary with success status
    """
    try:
        client = get_sns_client()
        if not client:
            error_msg = "AWS SNS client could not be initialized"
            print(f"❌ AWS SNS: {error_msg}")
            return {"success": False, "error": error_msg, "provider": "aws_sns"}

        print(f"📱 AWS SNS: Sending SMS to {phone_number}")

        # Send the SMS using AWS SNS
        response = client.publish(
            PhoneNumber=phone_number,
            Message=message,
            MessageAttributes={
                'AWS.SNS.SMS.SMSType': {
                    'DataType': 'String',
                    'StringValue': 'Transactional'  # Higher priority, better delivery
                }
            }
        )

        message_id = response.get('MessageId')
        print(f"✅ AWS SNS: SMS sent successfully - Message ID: {message_id}")

        # Log cost estimation
        estimated_cost = 0.045 if phone_number.startswith('+84') else 0.0075
        print(f"💰 AWS SNS: Cost estimate: ~${estimated_cost:.4f}")

        return {
            "success": True,
            "message_id": message_id,
            "to": phone_number,
            "estimated_cost": estimated_cost,
            "provider": "aws_sns"
        }

    except ClientError as e:
        error_code = e.response['Error']['Code']
        error_msg = f"AWS SNS error ({error_code}): {str(e)}"
        print(f"❌ AWS SNS: {error_msg}")
        return {"success": False, "error": error_msg, "provider": "aws_sns"}

    except Exception as e:
        error_msg = f"AWS SNS unexpected error: {str(e)}"
        print(f"❌ AWS SNS: {error_msg}")
        return {"success": False, "error": error_msg, "provider": "aws_sns"}


def send_sms(phone_number, message, fail_silently=True):
    """
    Send SMS using configured provider (AWS SNS, Vonage, or disabled).

    Args:
        phone_number (str): Recipient phone number
        message (str): SMS message content
        fail_silently (bool): Whether to fail silently on errors

    Returns:
        dict: Result dictionary with success status and message
    """
    # Get SMS provider from environment
    sms_provider = os.getenv('SMS_PROVIDER', 'aws_sns').lower()

    print(f"📱 SMS Provider: {sms_provider}")

    # Check if SMS is disabled
    if sms_provider == 'disabled':
        print(f"📱 SMS disabled (SMS_PROVIDER=disabled) - would send to {phone_number}")
        print(f"📝 Message: {message[:50]}...")
        return {
            "success": False,
            "error": "SMS disabled via SMS_PROVIDER setting",
            "disabled": True,
            "provider": "disabled"
        }

    # Format the recipient phone number
    formatted_phone = format_phone_number(phone_number)
    if not formatted_phone:
        error_msg = f"Invalid phone number format: {phone_number}"
        logger.error(error_msg)
        if not fail_silently:
            raise Exception(error_msg)
        return {"success": False, "error": error_msg}

    try:
        # Route to appropriate SMS provider
        if sms_provider == 'vonage':
            result = send_sms_vonage(formatted_phone, message)
        elif sms_provider == 'aws_sns':
            result = send_sms_aws_sns(formatted_phone, message)
        else:
            error_msg = f"Unknown SMS provider: {sms_provider}. Valid options: aws_sns, vonage, disabled"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

        # Log result
        if result.get("success"):
            logger.info(f"SMS sent successfully via {sms_provider} to {formatted_phone}")
        else:
            logger.error(f"SMS failed via {sms_provider}: {result.get('error')}")

        return result

    except Exception as e:
        error_msg = f"SMS sending failed: {str(e)}"
        logger.error(error_msg)
        if not fail_silently:
            raise Exception(error_msg)
        return {"success": False, "error": error_msg}


def send_task_assignment_sms(user, task, assigner):
    """
    DEPRECATED: Use utils.notifications.send_task_assignment_notifications instead.

    Send SMS notification when a user is assigned to a task using AWS SNS.
    This function is kept for backward compatibility but will be removed in future versions.

    Args:
        user: User object who was assigned to the task
        task: Task object that was assigned
        assigner: User object who assigned the task

    Returns:
        dict: Result dictionary with success status
    """
    print(f"⚠️  DEPRECATED: send_task_assignment_sms called for user {user.email}")
    print(f"⚠️  Please use utils.notifications.send_task_assignment_notifications instead")

    # Import here to avoid circular imports
    from utils.notifications import send_task_assignment_notifications

    # Use the new notification system
    result = send_task_assignment_notifications(user, task, assigner)

    # Return SMS result for backward compatibility
    return result.get('sms', {"success": False, "error": "SMS result not available"})
